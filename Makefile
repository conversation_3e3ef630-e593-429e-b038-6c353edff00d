# Build all by default, even if it's not first.
.DEFAULT_GOAL := all

.PHONY: all
all: fmt tidy

# ==============================================================================
# Build options

ROOT_PACKAGE=pingan.com/eping
VERSION_PACKAGE=pingan.com/eping/internal/version

# ==============================================================================
# Includes

# Make sure include common.mk at the first include line.
include scripts/make-rules/common.mk 
include scripts/make-rules/golang.mk
include scripts/make-rules/image.mk
include scripts/make-rules/docs.mk
include scripts/make-rules/tools.mk

# ==============================================================================
# Usage

## fmt: Reformat package sources (exclude vendor dir if existed).
.PHONY: fmt
fmt: tools.verify.golines tools.verify.goimports
	@echo "==> Formating codes"
	@$(FIND) -type f -name '*.go' | $(XARGS) gofmt -s -w
	@$(FIND) -type f -name '*.go' | $(XARGS) goimports -w -local $(ROOT_PACKAGE)
	@$(FIND) -type f -name '*.go' | $(XARGS) golines -w --max-len=135 --reformat-tags --ignore-generated .
	@$(GO) mod edit -fmt

## tidy: go mod tidy
.PHONY: tidy
tidy:
	@echo "==> go mod tidy"
	@$(GO) mod tidy

## vendor: go mod vendor
.PHONY: vendor
vendor:
	@echo "==> go mod vendor"
	@$(GO) mod vendor

## build: Build source code for host platform.
.PHONY: build
build:
	@$(MAKE) go.build

## build.multiarch: Build source code for multiple platforms.
.PHONY: build.multiarch
build.multiarch:
	@$(MAKE) go.build.multiarch

## image: Build docker images for host arch.
.PHONY: image
image:
	@$(MAKE) image.build

## image.multiarch: Build( and push) docker images for multiple platforms.
.PHONY: image.multiarch
image.multiarch:
	@$(MAKE) image.build.multiarch

## clean: Remove all files that are created by building and testing.
.PHONY: clean
clean:
	@echo "==> Cleaning all build and test outputs"
	@rm -rf $(OUTPUT_DIR)

## tools: Install dependent tools.
.PHONY: tools
tools:
	@$(MAKE) tools.install