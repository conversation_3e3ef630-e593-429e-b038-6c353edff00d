package version

import (
	"github.com/spf13/cobra"

	"pingan.com/eping/internal/version"
)

var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Display the version of eping.",
	RunE: func(cmd *cobra.Command, args []string) error {
		text, err := version.Get().Text()
		if err != nil {
			return err
		}

		cmd.Println(text)
		return nil
	},
}

// VersionCmd returns version command.
func VersionCmd() *cobra.Command {
	return versionCmd
}
