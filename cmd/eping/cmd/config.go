package cmd

var (
	file      string
	count     int
	workers   int
	delay     string
	tag       string
	filterExp string
	quiet     bool
	extended  bool
)

// init initializes command-line flags.
func init() {
	rootCmd.FParseErrWhitelist.UnknownFlags = true

	rootCmd.Flags().StringVarP(&file, "file", "f", "", "File of target list.")
	rootCmd.Flags().IntVarP(&count, "count", "c", 3, "Number of probe packets to send per target.")
	rootCmd.Flags().IntVarP(&workers, "workers", "w", 100, "Maximum concurrent workers.")
	rootCmd.Flags().StringVar(&delay, "delay", "200ms", "Delay between probe packets.")
	rootCmd.Flags().StringVarP(&tag, "tag", "t", "", "Regular expression to filter probes by tag.")
	rootCmd.Flags().StringVar(&filterExp, "filter", "", "Output filter expression, e.g. 'loss > 0', 'loss > 50%' or 'rtt <= 10ms'.")
	rootCmd.Flags().BoolVarP(&quiet, "quiet", "q", false, "Hide filtered results instead of dimming them.")
	rootCmd.Flags().BoolVarP(&extended, "extended", "x", false, "Show extended output.")
}
