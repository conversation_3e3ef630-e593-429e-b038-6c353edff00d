package cmd

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/spf13/cobra"
	ctrl "sigs.k8s.io/controller-runtime"

	"pingan.com/eping/cmd/eping/cmd/gen"
	cmdversion "pingan.com/eping/cmd/eping/cmd/version"
	"pingan.com/eping/internal/version"
	"pingan.com/eping/pkg/config"
	"pingan.com/eping/pkg/filter"
	"pingan.com/eping/pkg/output"
	"pingan.com/eping/pkg/probe"
)

var rootCmd = &cobra.Command{
	Use:   "eping",
	Short: fmt.Sprintf("eping %s", version.Get().Version),
	RunE: func(cmd *cobra.Command, args []string) error {
		return batch(ctrl.SetupSignalHandler())
	},
}

// Execute runs root command.
func Execute() {
	if !isBatchMode() {
		if err := nping(ctrl.SetupSignalHandler(), os.Args[1:]); err != nil {
			os.Exit(1)
		}
		return
	}

	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}

// init initializes root command.
func init() {
	rootCmd.AddCommand(cmdversion.VersionCmd())
	rootCmd.AddCommand(gen.GenCmd())
}

// isBatchMode checks if batch mode should be used.
func isBatchMode() bool {
	for _, arg := range os.Args[1:] {
		if arg == "-f" || arg == "--file" {
			return true
		}
		if strings.HasPrefix(arg, "--file=") {
			return true
		}
		if arg == "version" || arg == "gen" {
			return true
		}
		if arg == "help" || arg == "--help" || arg == "-help" || arg == "-h" {
			return true
		}
	}

	return false
}

// nping executes nping command.
func nping(ctx context.Context, args []string) error {
	if _, err := exec.LookPath("nping"); err != nil {
		return err
	}

	cmd := exec.CommandContext(ctx, "nping", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin

	return cmd.Run()
}

// batch executes batch probing.
func batch(ctx context.Context) error {
	f, err := filter.Parse(filterExp)
	if err != nil {
		return err
	}

	d, err := parseDelay(delay)
	if err != nil {
		return err
	}

	tasks, err := config.ParseWithTag(file, tag)
	if err != nil {
		return err
	}

	if len(tasks) == 0 {
		return nil
	}

	if count <= 0 {
		count = 1
	}

	for i := range tasks {
		tasks[i].Count = count
		tasks[i].Delay = d
	}

	sc := probe.NewScheduler(workers, probe.NewComposite())
	res, err := sc.Run(ctx, tasks)
	if err != nil {
		return err
	}

	f.Apply(res, quiet)
	output.Table(res, extended)

	return nil
}

// parseDelay parses delay string with default ms unit.
func parseDelay(delay string) (time.Duration, error) {
	d, err := time.ParseDuration(delay)
	if err != nil {
		d, err = time.ParseDuration(delay + "ms")
		if err != nil {
			return 0, fmt.Errorf("invalid delay %q: %v", delay, err)
		}
	}

	if d < time.Millisecond {
		d = time.Millisecond
	}

	return d, nil
}
