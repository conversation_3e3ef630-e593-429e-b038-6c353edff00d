package gen

import (
	"flag"

	ctrl "sigs.k8s.io/controller-runtime"
)

var (
	excel   string
	cluster string
)

func init() {
	// Register controller-runtime flags (including --kubeconfig).
	ctrl.RegisterFlags(flag.CommandLine)
	genCmd.Flags().AddGoFlagSet(flag.CommandLine)

	// Add Excel-specific flags.
	genCmd.Flags().StringVar(&excel, "excel", "", "Excel file path to parse targets from.")
	genCmd.Flags().StringVar(&cluster, "cluster", "", "Cluster name to filter targets (required when using --excel).")
}
