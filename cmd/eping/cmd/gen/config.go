package gen

import (
	"flag"

	ctrl "sigs.k8s.io/controller-runtime"
)

var (
	excel   string
	cluster string
)

func init() {
	// Register controller-runtime flags (including --kubeconfig).
	ctrl.RegisterFlags(flag.CommandLine)
	genCmd.Flags().AddGoFlagSet(flag.CommandLine)

	genCmd.Flags().StringVar(&excel, "excel", "", "Excel file to parse target list.")
	genCmd.Flags().StringVar(&cluster, "cluster", "", "Regular expression to filter targets by cluster name.")
}
