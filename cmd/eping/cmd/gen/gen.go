package gen

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"pingan.com/eping/pkg/generator"
)

var genCmd = &cobra.Command{
	Use:   "gen",
	Short: "Generate target list from Kubernetes cluster or Excel file.",
	RunE: func(cmd *cobra.Command, args []string) error {
		return gen(ctrl.SetupSignalHandler())
	},
}

// GenCmd returns gen command.
func GenCmd() *cobra.Command {
	return genCmd
}

// gen executes gen command.
func gen(ctx context.Context) error {
	if excel != "" {
		g, err := generator.NewExcelGenerator(excel, cluster)
		if err != nil {
			return err
		}

		targets, err := g.Collect(ctx)
		if err != nil {
			return fmt.Errorf("failed to collect targets from Excel %q: %v", excel, err)
		}

		fmt.Print(generator.Config(targets))
		return nil
	}

	cfg, err := ctrl.GetConfig()
	if err != nil {
		return err
	}

	c, err := client.New(cfg, client.Options{})
	if err != nil {
		return err
	}

	g := generator.NewK8sGenerator(c)
	targets, err := g.Collect(ctx)
	if err != nil {
		return fmt.Errorf("failed to collect targets from Kubernetes cluster: %v", err)
	}

	fmt.Print(generator.Config(targets))
	return nil
}
