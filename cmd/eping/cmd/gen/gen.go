package gen

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"pingan.com/eping/pkg/generator"
)

var genCmd = &cobra.Command{
	Use:   "gen",
	Short: "Generate target list from Kubernetes cluster or Excel file.",
	RunE: func(cmd *cobra.Command, args []string) error {
		return gen(ctrl.SetupSignalHandler())
	},
}

// GenCmd returns gen command.
func GenCmd() *cobra.Command {
	return genCmd
}

// gen executes gen command.
func gen(ctx context.Context) error {
	// Validate flags.
	if excel != "" && cluster == "" {
		return fmt.Errorf("--cluster is required when using --excel")
	}

	var g generator.Generator
	var targets *generator.ClusterTargetList
	var err error

	if excel != "" {
		// Generate from Excel file.
		g = generator.NewExcelGenerator(excel, cluster)
		targets, err = g.Collect(ctx)
		if err != nil {
			return fmt.Errorf("failed to collect targets from Excel: %v", err)
		}
	} else {
		// Generate from Kubernetes cluster.
		cfg, err := ctrl.GetConfig()
		if err != nil {
			return err
		}

		c, err := client.New(cfg, client.Options{})
		if err != nil {
			return err
		}

		g = generator.NewK8sGenerator(c)
		targets, err = g.Collect(ctx)
		if err != nil {
			return fmt.Errorf("failed to collect targets: %v", err)
		}
	}

	fmt.Print(generator.Config(targets))
	return nil
}
