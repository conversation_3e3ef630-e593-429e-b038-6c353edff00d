package generator

import (
	"context"
	"fmt"
	"sort"
	"strings"
)

// Generator generates eping target list.
type Generator interface {
	Collect(ctx context.Context) (*ClusterTargetList, error)
}

// ClusterTargetList holds collected targets.
type ClusterTargetList struct {
	Targets []Target
}

// Target represents a probe target.
type Target struct {
	Tag      string
	Protocol string
	IP       string
	Port     int
}

// Config generates target list configuration.
func Config(targets *ClusterTargetList) string {
	var sb strings.Builder
	sections := make(map[string][]Target)
	for _, target := range targets.Targets {
		key := fmt.Sprintf("%s:%s", target.Protocol, target.Tag)
		sections[key] = append(sections[key], target)
	}

	var keys []string
	for key := range sections {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	for i, key := range keys {
		targets := sections[key]
		if len(targets) == 0 {
			continue
		}

		sort.Slice(targets, func(i, j int) bool {
			if targets[i].Port != targets[j].Port {
				return targets[i].Port < targets[j].Port
			}
			return targets[i].IP < targets[j].IP
		})

		sb.WriteString(fmt.Sprintf("[%s]\n", key))
		for _, t := range targets {
			if t.Port > 0 {
				sb.WriteString(fmt.Sprintf("%s %d\n", t.IP, t.Port))
			} else {
				sb.WriteString(fmt.Sprintf("%s\n", t.IP))
			}
		}

		if i < len(keys)-1 {
			sb.WriteString("\n")
		}
	}

	return sb.String()
}
