package generator

import (
	"context"
	"fmt"
	"net"
	"sort"
	"strings"

	corev1 "k8s.io/api/core/v1"
	discoveryv1 "k8s.io/api/discovery/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// Generator generates eping target list from Kubernetes cluster.
type Generator struct {
	client client.Client
}

// New creates a generator.
func New(c client.Client) *Generator {
	return &Generator{
		client: c,
	}
}

// ClusterTargetList holds collected targets.
type ClusterTargetList struct {
	Targets []Target
}

// Target represents a probe target.
type Target struct {
	Tag      string
	Protocol string
	IP       string
	Port     int
}

// Collect collects targets from cluster.
func (g *Generator) Collect(ctx context.Context) (*ClusterTargetList, error) {
	targets, err := g.collectNodes(ctx)
	if err != nil {
		return nil, err
	}
	apiServer, err := g.collectService(ctx, "default", "kubernetes")
	if err != nil {
		return nil, err
	}
	etcd, err := g.collectService(ctx, "kube-system", "kube-etcd")
	if err != nil {
		return nil, err
	}
	eventer, err := g.collectService(ctx, "kube-system", "kube-etcd-eventer")
	if err != nil {
		return nil, err
	}
	dns, err := g.collectService(ctx, "kube-system", "kube-dns")
	if err != nil {
		return nil, err
	}
	dnsBackup, err := g.collectService(ctx, "kube-system", "kube-dns-backup")
	if err != nil {
		return nil, err
	}

	targets = append(targets, etcd...)
	targets = append(targets, apiServer...)
	targets = append(targets, eventer...)
	targets = append(targets, dns...)
	targets = append(targets, dnsBackup...)

	return &ClusterTargetList{
		Targets: targets,
	}, nil
}

// collectNodes collects node targets.
func (g *Generator) collectNodes(ctx context.Context) ([]Target, error) {
	var nodeList corev1.NodeList
	if err := g.client.List(ctx, &nodeList); err != nil {
		return nil, err
	}

	var targets []Target
	for _, node := range nodeList.Items {
		var ipv4, ipv6 string
		for _, addr := range node.Status.Addresses {
			if addr.Type != corev1.NodeInternalIP {
				continue
			}

			ip := net.ParseIP(addr.Address)
			if ip == nil {
				continue
			}

			if ip.To4() != nil {
				ipv4 = addr.Address
			} else {
				ipv6 = addr.Address
			}
		}

		if ipv4 != "" {
			targets = append(targets, Target{
				Tag:      "node",
				Protocol: "icmp",
				IP:       ipv4,
			})
			targets = append(targets, Target{
				Tag:      "kubelet",
				Protocol: "tcp",
				IP:       ipv4,
				Port:     10250,
			})
		}

		if ipv6 != "" {
			targets = append(targets, Target{
				Tag:      "node-ipv6",
				Protocol: "icmp",
				IP:       ipv6,
			})
			targets = append(targets, Target{
				Tag:      "kubelet-ipv6",
				Protocol: "tcp",
				IP:       ipv6,
				Port:     10250,
			})
		}
	}

	return targets, nil
}

// collectService collects service targets.
func (g *Generator) collectService(ctx context.Context, namespace, svcName string) ([]Target, error) {
	var esList discoveryv1.EndpointSliceList
	if err := g.client.List(
		ctx,
		&esList,
		client.InNamespace(namespace),
		client.MatchingLabels{
			"kubernetes.io/service-name": svcName,
		},
	); err != nil {
		return nil, err
	}

	var targets []Target
	for _, es := range esList.Items {
		tag := svcName
		if es.AddressType == discoveryv1.AddressTypeIPv6 {
			tag += "-ipv6"
		}
		for _, port := range es.Ports {
			if port.Port == nil || *port.Protocol != corev1.ProtocolTCP {
				continue
			}
			for _, ep := range es.Endpoints {
				for _, addr := range ep.Addresses {
					targets = append(targets, Target{
						Tag:      tag,
						Protocol: strings.ToLower(string(*port.Protocol)),
						IP:       addr,
						Port:     int(*port.Port),
					})
				}
			}
		}
	}

	return targets, nil
}

// Config generates target list configuration.
func (g *Generator) Config(targets *ClusterTargetList) string {
	var sb strings.Builder
	sections := make(map[string][]Target)
	for _, target := range targets.Targets {
		key := fmt.Sprintf("%s:%s", target.Protocol, target.Tag)
		sections[key] = append(sections[key], target)
	}

	var keys []string
	for key := range sections {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	for i, key := range keys {
		targets := sections[key]
		if len(targets) == 0 {
			continue
		}

		sort.Slice(targets, func(i, j int) bool {
			if targets[i].Port != targets[j].Port {
				return targets[i].Port < targets[j].Port
			}
			return targets[i].IP < targets[j].IP
		})

		sb.WriteString(fmt.Sprintf("[%s]\n", key))
		for _, t := range targets {
			if t.Port > 0 {
				sb.WriteString(fmt.Sprintf("%s %d\n", t.IP, t.Port))
			} else {
				sb.WriteString(fmt.Sprintf("%s\n", t.IP))
			}
		}

		if i < len(keys)-1 {
			sb.WriteString("\n")
		}
	}

	return sb.String()
}
