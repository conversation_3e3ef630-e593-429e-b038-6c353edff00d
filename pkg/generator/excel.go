package generator

import (
	"context"
	"fmt"
	"net"
	"strings"

	"github.com/xuri/excelize/v2"
)

// ExcelGenerator generates eping target list from Excel file.
type ExcelGenerator struct {
	filename string
	cluster  string
}

// NewExcelGenerator creates an Excel generator.
func NewExcelGenerator(filename, cluster string) *ExcelGenerator {
	return &ExcelGenerator{
		filename: filename,
		cluster:  cluster,
	}
}

// Collect collects targets from Excel file.
func (g *ExcelGenerator) Collect(ctx context.Context) (*ClusterTargetList, error) {
	f, err := excelize.OpenFile(g.filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			// Log error but don't fail the operation.
		}
	}()

	sheetName := "全量设备列表"
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("failed to read sheet %q: %v", sheetName, err)
	}

	if len(rows) == 0 {
		return &ClusterTargetList{}, nil
	}

	// Find column indices.
	header := rows[0]
	clusterColIdx := -1
	hostIPColIdx := -1

	for i, col := range header {
		switch col {
		case "集群":
			clusterColIdx = i
		case "host_ip":
			hostIPColIdx = i
		}
	}

	if clusterColIdx == -1 {
		return nil, fmt.Errorf("column '集群' not found in sheet %q", sheetName)
	}
	if hostIPColIdx == -1 {
		return nil, fmt.Errorf("column 'host_ip' not found in sheet %q", sheetName)
	}

	var targets []Target
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		if len(row) <= clusterColIdx || len(row) <= hostIPColIdx {
			continue
		}

		rowCluster := strings.TrimSpace(row[clusterColIdx])
		hostIP := strings.TrimSpace(row[hostIPColIdx])

		// Filter by cluster name.
		if rowCluster != g.cluster {
			continue
		}

		// Validate IP address.
		if net.ParseIP(hostIP) == nil {
			continue
		}

		// Generate ICMP target for the IP.
		targets = append(targets, Target{
			Tag:      g.cluster,
			Protocol: "icmp",
			IP:       hostIP,
		})
	}

	return &ClusterTargetList{
		Targets: targets,
	}, nil
}
