package generator

import (
	"context"
	"fmt"
	"strings"

	"github.com/xuri/excelize/v2"
)

// excelGenerator generates eping target list from Excel file.
type excelGenerator struct {
	file    string
	cluster string
}

// NewExcelGenerator creates an Excel generator.
func NewExcelGenerator(file, cluster string) Generator {
	return &excelGenerator{
		file:    file,
		cluster: cluster,
	}
}

// Collect collects targets from Excel file.
func (g *excelGenerator) Collect(ctx context.Context) (*ClusterTargetList, error) {
	f, err := excelize.OpenFile(g.file)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = f.Close()
	}()

	sheet := "全量设备列表"
	rows, err := f.GetRows(sheet)
	if err != nil {
		return nil, err
	}

	if len(rows) == 0 {
		return nil, nil
	}

	header := rows[0]
	clusterColIdx := -1
	hostIPColIdx := -1
	for i, col := range header {
		switch col {
		case "集群":
			clusterColIdx = i
		case "host_ip":
			hostIPColIdx = i
		}
	}

	if clusterColIdx == -1 {
		return nil, fmt.Errorf("column '集群' not found in sheet %q", sheet)
	}
	if hostIPColIdx == -1 {
		return nil, fmt.Errorf("column 'host_ip' not found in sheet %q", sheet)
	}

	var targets []Target
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		if len(row) <= clusterColIdx || len(row) <= hostIPColIdx {
			continue
		}

		cluster := strings.TrimSpace(row[clusterColIdx])
		hostIP := strings.TrimSpace(row[hostIPColIdx])
		if cluster != g.cluster {
			continue
		}

		targets = append(targets, Target{
			Tag:      g.cluster,
			Protocol: "icmp",
			IP:       hostIP,
		})
	}

	return &ClusterTargetList{
		Targets: targets,
	}, nil
}
