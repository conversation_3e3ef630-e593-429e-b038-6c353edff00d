package probe

import (
	"context"
	"fmt"
)

// CompositeProber routes probe tasks to appropriate probers based on protocol.
type CompositeProber struct {
	icmp   Prober
	tcpudp Prober
}

// NewComposite creates a composite prober.
func NewComposite() *CompositeProber {
	return &CompositeProber{
		icmp:   NewPing(),
		tcpudp: NewNping(),
	}
}

// Probe routes task to appropriate prober by protocol.
func (p *CompositeProber) Probe(ctx context.Context, task *Task) (*Stats, error) {
	switch task.Protocol {
	case "ICMP":
		return p.icmp.Probe(ctx, task)
	case "TCP", "UDP":
		return p.tcpudp.Probe(ctx, task)
	default:
		return nil, fmt.Errorf("unsupported protocol: %s", task.Protocol)
	}
}
