package probe

import (
	"context"
	"errors"
	"fmt"
	"net"
	"os/exec"
	"regexp"
	"strconv"
)

var (
	// Matches both formats:
	// 3 packets transmitted, 3 packets received, 0% packet loss
	// 4 packets transmitted, 4 received, 0% packet loss
	pingStatsRegex = regexp.MustCompile(`(\d+) packets transmitted, (\d+)(?: packets)? received, ([0-9.]+)% packet loss`)

	// Matches both formats:
	// rtt min/avg/max/mdev = 415.194/506.812/598.430/91.618 ms
	// round-trip min/avg/max = 272.558/471.431/610.040 ms
	pingRTTRegex = regexp.MustCompile(
		`(?:rtt min/avg/max/mdev|round-trip min/avg/max) = ([0-9.]+)/([0-9.]+)/([0-9.]+)(?:/[0-9.]+)? ms`,
	)
)

// PingProber represents a ping-based ICMP network prober.
type Ping<PERSON>rober struct{}

// NewPing creates a ping prober.
func NewPing() *PingProber {
	return &PingProber{}
}

// Probe executes ICMP probe task.
func (p *PingProber) Probe(ctx context.Context, task *Task) (*Stats, error) {
	if task.Protocol != "ICMP" {
		return nil, fmt.Errorf("ping prober only supports ICMP protocol, got %s", task.Protocol)
	}

	args, err := p.buildArgs(task)
	if err != nil {
		return nil, err
	}

	cmd := exec.CommandContext(ctx, "ping", args...)
	output, err := cmd.CombinedOutput()

	// Parse output even if ping command failed (e.g., 100% packet loss) ping
	// exits with code 1 when all packets are lost, but still provides valid
	// statistics.
	stats, pErr := p.parseOutput(string(output), task)
	if pErr != nil {
		if err != nil {
			if len(output) == 0 {
				return nil, err
			}
			return nil, errors.New(string(output))
		}
		return nil, pErr
	}

	return stats, nil
}

// buildArgs builds ping command arguments.
func (p *PingProber) buildArgs(task *Task) ([]string, error) {
	args := []string{
		"-q",
		"-W", "1",
		"-c", strconv.Itoa(task.Count),
		"-i", strconv.FormatFloat(task.Delay.Seconds(), 'f', 3, 64),
		task.Dst,
	}

	if ip := net.ParseIP(task.Dst); ip != nil && ip.To4() == nil {
		args = append(args, "-6")
	}

	return args, nil
}

// parseOutput parses ping output.
func (p *PingProber) parseOutput(output string, task *Task) (*Stats, error) {
	statsMatches := pingStatsRegex.FindStringSubmatch(output)
	if len(statsMatches) != 4 {
		return nil, fmt.Errorf("malformed ping statistics output: %q", output)
	}

	tx, err := strconv.Atoi(statsMatches[1])
	if err != nil {
		return nil, err
	}
	rx, err := strconv.Atoi(statsMatches[2])
	if err != nil {
		return nil, err
	}
	lossRate, err := strconv.ParseFloat(statsMatches[3], 64)
	if err != nil {
		return nil, err
	}

	var minRTT, maxRTT, avgRTT float64
	rttMatches := pingRTTRegex.FindStringSubmatch(output)
	if len(rttMatches) == 4 {
		minRTT, err = strconv.ParseFloat(rttMatches[1], 64)
		if err != nil {
			return nil, err
		}
		avgRTT, err = strconv.ParseFloat(rttMatches[2], 64)
		if err != nil {
			return nil, err
		}
		maxRTT, err = strconv.ParseFloat(rttMatches[3], 64)
		if err != nil {
			return nil, err
		}
	}

	return &Stats{
		Tag:      task.Tag,
		Protocol: task.Protocol,
		Dst:      task.Dst,
		Port:     0,
		Tx:       tx,
		Rx:       rx,
		Lost:     tx - rx,
		LossRate: lossRate,
		MinRTT:   minRTT,
		MaxRTT:   maxRTT,
		AvgRTT:   avgRTT,
	}, nil
}
