package probe

import (
	"context"

	"golang.org/x/sync/errgroup"
)

// Scheduler represents a task scheduler.
type Scheduler struct {
	workers int
	prober  Prober
}

// NewScheduler creates a scheduler.
func NewScheduler(workers int, prober Prober) *Scheduler {
	if workers <= 0 {
		workers = 1
	}

	return &Scheduler{
		workers: workers,
		prober:  prober,
	}
}

// Run executes probe tasks concurrently.
func (s *Scheduler) Run(ctx context.Context, tasks []Task) (*Result, error) {
	if len(tasks) == 0 {
		return nil, nil
	}

	eg, eCtx := errgroup.WithContext(ctx)
	eg.SetLimit(s.workers)

	statsCh := make(chan *Stats, len(tasks))
	for i := range tasks {
		task := &tasks[i]
		eg.Go(func() error {
			stats, err := s.prober.Probe(eCtx, task)
			if err != nil {
				return err
			}

			select {
			case <-eCtx.Done():
				return eCtx.Err()
			case statsCh <- stats:
				return nil
			}
		})
	}

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	close(statsCh)
	res := &Result{
		StatSets: make([]Stats, 0, len(tasks)),
	}

	for stats := range statsCh {
		res.StatSets = append(res.StatSets, *stats)
	}

	return res, nil
}
