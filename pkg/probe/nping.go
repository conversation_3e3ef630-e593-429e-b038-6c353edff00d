package probe

import (
	"context"
	"errors"
	"fmt"
	"net"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
)

var (
	// Matches both formats:
	// packets sent: 2 | Rcvd: 2 | Lost: 0 (0.00%)
	// packets sent: 2 (56B) | Rcvd: 2 (56B) | Lost: 0 (0.00%)
	rawStatsRegex = regexp.MustCompile(`packets sent:\s*(\d+).*\|\s*Rcvd:\s*(\d+).*\|\s*Lost:\s*(\d+)\s*\(([0-9.]+)%\)`)

	// TCP connection attempts: 4 | Successful connections: 0 | Failed: 4 (100.00%)
	tcpStatsRegex = regexp.MustCompile(
		`TCP connection attempts:\s*(\d+)\s*\|\s*Successful connections:\s*(\d+)\s*\|\s*Failed:\s*(\d+)\s*\(([0-9.]+)%\)`,
	)

	// Matches both formats:
	// Max rtt: N/A | Min rtt: N/A | Avg rtt: N/A
	// Max rtt: 71.252ms | Min rtt: 68.305ms | Avg rtt: 69.778ms
	// Max rtt: 1.953525380810766e+15ms | Min rtt: 1.953525380810766e+15ms | Avg rtt: 1.953525380810766e+15ms
	npingRTTRegex = regexp.MustCompile(`Max rtt:\s*([0-9.e+-]+ms|N/A).*Min rtt:\s*([0-9.e+-]+ms|N/A).*Avg rtt:\s*([0-9.e+-]+ms|N/A)`)
)

// NpingProber represents an nping-based network prober.
type NpingProber struct{}

// NewNping creates an nping prober.
func NewNping() *NpingProber {
	return &NpingProber{}
}

// Probe executes TCP/UDP probe task.
func (p *NpingProber) Probe(ctx context.Context, task *Task) (*Stats, error) {
	if task.Protocol != "TCP" && task.Protocol != "UDP" {
		return nil, fmt.Errorf("nping prober only supports TCP/UDP protocol, got %s", task.Protocol)
	}

	args, err := p.buildArgs(task)
	if err != nil {
		return nil, err
	}

	cmd := exec.CommandContext(ctx, "nping", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		if len(output) == 0 {
			return nil, err
		}
		return nil, errors.New(string(output))
	}

	return p.parseOutput(string(output), task)
}

// buildArgs builds nping command arguments.
func (p *NpingProber) buildArgs(task *Task) ([]string, error) {
	args := []string{
		"-q",
		"-c", strconv.Itoa(task.Count),
		"--delay", task.Delay.String(),
		task.Dst,
	}

	if ip := net.ParseIP(task.Dst); ip != nil && ip.To4() == nil {
		args = append(args, "-6")
	}

	switch task.Protocol {
	case "TCP":
		args = append(args, "--tcp-connect", "-p", strconv.Itoa(task.Port))
	case "UDP":
		args = append(args, "--udp", "-p", strconv.Itoa(task.Port))
	}

	return args, nil
}

// parseOutput parses nping output.
func (p *NpingProber) parseOutput(output string, task *Task) (*Stats, error) {
	var statsMatches []string
	switch task.Protocol {
	case "TCP":
		statsMatches = tcpStatsRegex.FindStringSubmatch(output)
	case "UDP":
		statsMatches = rawStatsRegex.FindStringSubmatch(output)
	}
	if len(statsMatches) != 5 {
		return nil, fmt.Errorf("malformed Nping statistics output: %q", output)
	}

	rttMatches := npingRTTRegex.FindStringSubmatch(output)
	if len(rttMatches) != 4 {
		return nil, fmt.Errorf("malformed Nping RTT output: %q", output)
	}

	tx, err := strconv.Atoi(statsMatches[1])
	if err != nil {
		return nil, err
	}
	rx, err := strconv.Atoi(statsMatches[2])
	if err != nil {
		return nil, err
	}
	lost, err := strconv.Atoi(statsMatches[3])
	if err != nil {
		return nil, err
	}
	lossRate, err := strconv.ParseFloat(statsMatches[4], 64)
	if err != nil {
		return nil, err
	}
	maxRTT, err := parseRTT(rttMatches[1])
	if err != nil {
		return nil, err
	}
	minRTT, err := parseRTT(rttMatches[2])
	if err != nil {
		return nil, err
	}
	avgRTT, err := parseRTT(rttMatches[3])
	if err != nil {
		return nil, err
	}

	// nping RTT BUG.
	if minRTT != 0 && maxRTT == 0 && avgRTT == 0 {
		maxRTT = minRTT
		avgRTT = minRTT
	}

	return &Stats{
		Tag:      task.Tag,
		Protocol: task.Protocol,
		Dst:      task.Dst,
		Port:     task.Port,
		Tx:       tx,
		Rx:       rx,
		Lost:     lost,
		LossRate: lossRate,
		MinRTT:   minRTT,
		MaxRTT:   maxRTT,
		AvgRTT:   avgRTT,
	}, nil
}

// parseRTT parses RTT string.
func parseRTT(rtt string) (float64, error) {
	if rtt == "N/A" {
		return 0, nil
	}

	rtt = strings.TrimSuffix(rtt, "ms")
	value, err := strconv.ParseFloat(rtt, 64)
	if err != nil {
		return 0, err
	}

	// Sanity check: RTT should be reasonable (0-300000ms = 5 minutes max)
	// Values beyond this are likely parsing errors or network issues.
	if value < 0 || value > 300000 {
		// For unreasonable values, return 0 instead of error to avoid breaking
		// the entire probe This allows the probe to continue and show other
		// valid statistics.
		return 0, nil
	}

	return value, nil
}
