package probe

import (
	"context"
	"time"
)

// Style represents how stats should be displayed.
type Style int

const (
	// StyleNormal represents normal display (matches filter or no filter applied).
	StyleNormal Style = iota
	// StyleDimmed represents dimmed display (doesn't match filter criteria).
	StyleDimmed
	// StyleHidden represents hidden display (completely filtered out).
	StyleHidden
)

// Prober defines the interface for network probing.
type Prober interface {
	// Probe executes a single probe task and returns the result.
	Probe(ctx context.Context, task *Task) (*Stats, error)
}

// Task represents a single probe task.
type Task struct {
	Tag      string
	Protocol string
	Dst      string
	Port     int
	Count    int
	Delay    time.Duration
}

// Stats represents probe statistics for any protocol.
type Stats struct {
	Style    Style
	Tag      string
	Protocol string
	Dst      string
	Port     int
	Tx       int
	Rx       int
	Lost     int
	LossRate float64
	MinRTT   float64
	MaxRTT   float64
	AvgRTT   float64
}

// Result represents a collection of probe results.
type Result struct {
	StatSets []Stats
}
