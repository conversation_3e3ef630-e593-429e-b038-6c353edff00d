package config

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"regexp"
	"strconv"
	"strings"

	"pingan.com/eping/pkg/probe"
)

// sectionRegex matches section headers like [protocol:tag].
var sectionRegex = regexp.MustCompile(`^\[([^:]+)(?::([^]]+))?\]$`)

// Parse parses target list file.
func Parse(filename string) ([]probe.Task, error) {
	return ParseWithTag(filename, "")
}

// ParseWithTag parses target list file with tag filter.
func ParseWithTag(filename, pattern string) ([]probe.Task, error) {
	if filename == "-" {
		return parse(os.Stdin, pattern)
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	return parse(file, pattern)
}

// parse parses target list from reader.
func parse(reader io.Reader, pattern string) ([]probe.Task, error) {
	var re *regexp.Regexp
	if pattern != "" {
		var err error
		re, err = regexp.Compile(pattern)
		if err != nil {
			return nil, err
		}
	}

	skipSection := false
	taskMap := make(map[string]*probe.Task)

	var protocol, tag string
	scanner := bufio.NewScanner(reader)
	for scanner.Scan() {
		target := strings.TrimSpace(scanner.Text())
		if target == "" || strings.HasPrefix(target, "#") {
			continue
		}

		if matches := sectionRegex.FindStringSubmatch(target); matches != nil {
			protocol = strings.ToUpper(matches[1])
			if !isSupportedProtocol(protocol) {
				return nil, fmt.Errorf("unsupported protocol: %s", matches[1])
			}

			tag = matches[2]
			if re != nil {
				skipSection = !re.MatchString(tag)
			}
			continue
		}

		if protocol == "" || skipSection {
			continue
		}

		task, err := parseTarget(target, protocol, tag)
		if err != nil {
			return nil, fmt.Errorf("invalid target %q: %v", target, err)
		}

		key := fmt.Sprintf("%s-%s-%d", task.Protocol, task.Dst, task.Port)
		existing, ok := taskMap[key]
		if !ok || (task.Tag != "" && existing.Tag == "") {
			taskMap[key] = task
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	var tasks []probe.Task
	for _, task := range taskMap {
		tasks = append(tasks, *task)
	}

	return tasks, nil
}

// isSupportedProtocol checks protocol support.
func isSupportedProtocol(protocol string) bool {
	switch protocol {
	case "ICMP", "TCP", "UDP":
		return true
	default:
		return false
	}
}

// parseTarget parses target address and port.
func parseTarget(target, protocol, tag string) (*probe.Task, error) {
	parts := strings.Fields(target)
	task := &probe.Task{
		Protocol: protocol,
		Dst:      parts[0],
		Tag:      tag,
	}

	if protocol == "ICMP" {
		return task, nil
	}

	if len(parts) < 2 {
		return nil, fmt.Errorf("%s target requires port", protocol)
	}

	port, err := strconv.Atoi(parts[1])
	if err != nil {
		return nil, fmt.Errorf("invalid port %q: %v", parts[1], err)
	}
	if port < 1 || port > 65535 {
		return nil, fmt.Errorf("port %d out of range (1-65535)", port)
	}
	task.Port = port

	return task, nil
}
