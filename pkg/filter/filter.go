package filter

import (
	"fmt"
	"strconv"
	"strings"

	"pingan.com/eping/pkg/probe"
)

// operators defines the supported comparison operators in order of precedence.
var operators = []string{">=", "<=", "==", "!=", ">", "<"}

// fields defines the fields that can be used in filter expressions.
var fields = map[string]bool{
	"loss": true,
	"rtt":  true,
}

// Filter represents a parsed filter with its configuration.
type Filter struct {
	field      string
	operator   string
	value      string
	floatValue float64
}

// Parse creates filter from expression.
func Parse(expr string) (*Filter, error) {
	if expr == "" {
		return &Filter{}, nil
	}

	var field, operator, value string
	for _, op := range operators {
		if !strings.Contains(expr, op) {
			continue
		}

		parts := strings.SplitN(expr, op, 2)
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid filter: %s", expr)
		}

		operator = op
		field = strings.TrimSpace(parts[0])
		value = strings.Trim(parts[1], " '\"")
		break
	}

	if operator == "" {
		return nil, fmt.Errorf("invalid filter: %s", expr)
	}
	if !fields[field] {
		return nil, fmt.Errorf("unsupported filter field: %s", field)
	}

	// Convert loss with % to lossRate field.
	if field == "loss" && strings.HasSuffix(value, "%") {
		field = "lossRate"
		value = strings.TrimSuffix(value, "%")
	}

	// Parse value to float64.
	if field == "rtt" && strings.HasSuffix(value, "ms") {
		value = strings.TrimSuffix(value, "ms")
	}
	fv, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid filter: %s", expr)
	}

	return &Filter{
		field:      field,
		operator:   operator,
		value:      value,
		floatValue: fv,
	}, nil
}

// Apply applies filter to probe results.
func (f *Filter) Apply(result *probe.Result, quiet bool) {
	if result == nil {
		return
	}

	if f.operator == "" {
		for i := 0; i < len(result.StatSets); i++ {
			result.StatSets[i].Style = probe.StyleNormal
		}
		return
	}

	for i, stats := range result.StatSets {
		if f.Match(&stats) {
			result.StatSets[i].Style = probe.StyleNormal
			continue
		}

		if quiet {
			result.StatSets[i].Style = probe.StyleHidden
		} else {
			result.StatSets[i].Style = probe.StyleDimmed
		}
	}
}

// Match checks if stats match filter.
func (f *Filter) Match(stats *probe.Stats) bool {
	var v float64
	switch f.field {
	case "loss":
		v = float64(stats.Lost)
	case "lossRate":
		v = stats.LossRate
	case "rtt":
		v = stats.AvgRTT
	}

	switch f.operator {
	case "==":
		return v == f.floatValue
	case "!=":
		return v != f.floatValue
	case ">":
		return v > f.floatValue
	case ">=":
		return v >= f.floatValue
	case "<":
		return v < f.floatValue
	case "<=":
		return v <= f.floatValue
	}

	return false
}
