package output

import (
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"

	"github.com/olekukonko/tablewriter"

	"pingan.com/eping/pkg/probe"
)

const (
	colorReset = "\033[0m"
	colorDim   = "\033[2m"
)

// Table prints probe results as table.
func Table(result *probe.Result, extended bool) {
	if result == nil || len(result.StatSets) == 0 {
		return
	}

	render := false
	for _, s := range result.StatSets {
		if s.Style != probe.StyleHidden {
			render = true
			break
		}
	}

	if !render {
		return
	}

	sort.Slice(result.StatSets, func(i, j int) bool {
		a, b := result.StatSets[i], result.StatSets[j]
		if a.Style != b.Style {
			return a.Style < b.Style
		}
		aHasTag := a.Tag != ""
		bHasTag := b.Tag != ""
		if aHasTag != bHasTag {
			return aHasTag
		}
		if a.Tag != b.Tag {
			return a.Tag < b.Tag
		}
		if a.Protocol != b.Protocol {
			return a.Protocol < b.Protocol
		}
		if a.Port != b.Port {
			return a.Port < b.Port
		}
		return a.Dst < b.Dst
	})

	table := tablewriter.NewWriter(os.Stdout)
	table.SetBorder(false)
	table.SetCenterSeparator("")
	table.SetColumnSeparator("")
	table.SetRowSeparator("")
	table.SetHeaderLine(false)
	table.SetTablePadding("   ")
	table.SetNoWhiteSpace(true)
	table.SetHeaderAlignment(tablewriter.ALIGN_LEFT)
	table.SetAlignment(tablewriter.ALIGN_LEFT)

	var headers []string
	if extended {
		headers = []string{"PROTOCOL", "DST", "PORT", "TAG", "TX", "RX", "LOST", "LOSS%", "MIN-RTT", "AVG-RTT", "MAX-RTT"}
	} else {
		headers = []string{"PROTOCOL", "DST", "PORT", "TAG", "TX", "LOST", "LOSS%", "RTT"}
	}
	table.SetHeader(headers)

	for _, stats := range result.StatSets {
		if stats.Style == probe.StyleHidden {
			continue
		}

		applyStyle := func(text string) string {
			switch stats.Style {
			case probe.StyleDimmed:
				return colorDim + text + colorReset
			default:
				return text
			}
		}

		portStr := ""
		if stats.Protocol != "ICMP" {
			portStr = strconv.Itoa(stats.Port)
		}

		protocol := applyStyle(strings.ToUpper(stats.Protocol))
		dst := applyStyle(stats.Dst)
		port := applyStyle(portStr)
		tx := applyStyle(strconv.Itoa(stats.Tx))
		lost := applyStyle(strconv.Itoa(stats.Lost))
		lossRate := applyStyle(fmt.Sprintf("%.2f%%", stats.LossRate))
		tag := applyStyle(stats.Tag)

		var row []string
		if extended {
			rx := applyStyle(strconv.Itoa(stats.Rx))
			minRTT := applyStyle(formatRTT(stats.MinRTT))
			avgRTT := applyStyle(formatRTT(stats.AvgRTT))
			maxRTT := applyStyle(formatRTT(stats.MaxRTT))
			row = []string{protocol, dst, port, tag, tx, rx, lost, lossRate, minRTT, avgRTT, maxRTT}
		} else {
			rtt := applyStyle(formatRTT(stats.AvgRTT))
			row = []string{protocol, dst, port, tag, tx, lost, lossRate, rtt}
		}
		table.Append(row)
	}

	table.Render()
}

// formatRTT formats RTT value.
func formatRTT(rtt float64) string {
	if rtt == 0 {
		return "-"
	}

	if rtt < 1 {
		return fmt.Sprintf("%.3fms", rtt)
	}

	return fmt.Sprintf("%.2fms", rtt)
}
