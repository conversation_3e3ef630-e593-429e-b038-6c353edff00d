#!/bin/sh

if test ! -f "$1" ; then
    echo "file does not exist: $1"
    exit 1
fi 

# Temp commit msg file.
random=$( (whoami; hostname; date; cat $1; echo $RANDOM) | git hash-object --stdin)
email=$(git config user.email)
username=$(git config user.name)

dest="$1.tmp.${random}"

if ! git stripspace --strip-comments < "$1" > "${dest}" ; then
    echo "can't strip comments from $1"
    exit 1
fi

# Check commit message with pattern string.
check_msg=$(cat $1 | egrep "^(feat|perf|refactor|style|fix|docs|test|build|ci|chore|release|cherry-pick)(\w+)?:\s(\S|\w)+")
if [ "$check_msg" = "" ]; then
	echo -e "invalid commit message format, please use the correct format: \
    \nfeat: Add comments \
    \nfix: Handle events on blur (close #28)"
	exit 1
fi

if ! git stripspace --strip-comments < "$1" > "${dest}" ; then
    echo "can't strip comments from $1"
fi

if test ! -s "${dest}" ; then
    echo "file is empty: $1"
    exit 1
fi

# Avoid the --in-place option which only appeared in Git 2.8.
# Avoid the --if-exists option which only appeared in Git 2.15.
if ! git -c trailer.ifexists=doNothing interpret-trailers \
      --trailer "Signed-off-by: $username <$email>" < "$1" > "${dest}" ; then
  echo "cannot insert signed-off-by line in $1"
  exit 1
fi

if ! mv "${dest}" "$1" ; then
  echo "cannot mv ${dest} to $1"
  exit 1
fi