DEP_TOOLS ?= goimports golines golangci-lint
OTHER_TOOLS ?=

.PHONY: tools.install
tools.install: $(addprefix tools.install., $(DEP_TOOLS) ${OTHER_TOOLS})

.PHONY: tools.install.%
tools.install.%:
	@echo "==> Installing $*"
	@$(MAKE) install.$*

.PHONY: tools.verify.%
tools.verify.%:
	@if ! which $* &>/dev/null; then $(MAKE) tools.install.$*; fi

.PHONY: install.goimports
install.goimports:
	@$(GO) install golang.org/x/tools/cmd/goimports@latest

.PHONY: install.golines
install.golines:
	@$(GO) install github.com/segmentio/golines@latest

.PHONY: install.golangci-lint
install.golangci-lint:
	@$(GO) install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

.PHONY: install.markdownlint-cli2
install.markdownlint-cli2:
	@npm install markdownlint-cli2 --global