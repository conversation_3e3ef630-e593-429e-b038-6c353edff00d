DOCKER := docker

REGISTRY_PREFIX ?= ghcr.io
BUILDER_IMAGE ?= golang:$(GO_VERSION)
BASE_IMAGE ?= instrumentisto/nmap:7.95

# Determine image files by looking into images/*/Dockerfile.
IMAGES_DIR ?= $(wildcard ${ROOT_DIR}/images/*)
# Determine images names by stripping out the dir names.
IMAGES ?= $(filter-out tools,$(foreach image,${IMAGES_DIR},$(notdir ${image})))

ifeq (${IMAGES},)
  $(error Could not determine IMAGES, set ROOT_DIR or run in source dir)
endif

.PHONY: image.build
image.build: $(addprefix image.build., $(addprefix $(IMAGE_PLAT)., $(IMAGES)))

.PHONY: image.build.multiarch
image.build.multiarch: $(addprefix image.build., $(addprefix $(subst $(SPACE),$(COMMA),$(PLATFORMS))., $(IMAGES)))

.PHONY: image.build.%
image.build.%:
	$(eval IMAGE := $(word 2,$(subst ., ,$*)))
	$(eval PLATFORMS := $(word 1,$(subst ., ,$*)))
	$(eval IMAGE_PLATS := $(subst _,/,$(PLATFORMS)))
	$(eval BUILD_SUFFIX := -f $(ROOT_DIR)/images/$(IMAGE)/Dockerfile)
	$(eval BUILD_SUFFIX += --build-arg BUILDER_IMAGE=$(BUILDER_IMAGE) --build-arg BASE_IMAGE=$(BASE_IMAGE))
	$(eval BUILD_SUFFIX += -t $(REGISTRY_PREFIX)/$(IMAGE):$(VERSION) $(ROOT_DIR))
	@echo "==> Building docker image $(IMAGE) $(VERSION) for $(IMAGE_PLATS)"
	$(DOCKER) buildx build --platform $(IMAGE_PLATS) $(BUILD_SUFFIX) $(if $(findstring $(COMMA),$(IMAGE_PLATS)),--push --builder multi-platform,--load --builder default) 

.PHONY: image.push
image.push: $(addprefix image.push., $(IMAGES))

.PHONY: image.push.%
image.push.%:
	$(eval IMAGE := $*)
	@echo "==> Pushing image $(IMAGE) $(VERSION) to $(REGISTRY_PREFIX)"
	$(DOCKER) push $(REGISTRY_PREFIX)/$(IMAGE):$(VERSION)