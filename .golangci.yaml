linters-settings:
  gocritic:
    enabled-tags:
      # - performance
      - diagnostic
      - experimental
      - opinionated
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint
  gofmt:
    rewrite-rules:
      - pattern: 'interface{}'
        replacement: 'any'
  goimports:
    local-prefixes: pingan.com/eping

linters:
  disable-all: true
  enable:
    - goheader
    - bodyclose
    - dogsled
    - errcheck
    - gocheckcompilerdirectives
    - gocritic
    - gofmt
    - goimports
    - goprintffuncname
    - gosimple
    - govet
    - ineffassign
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - staticcheck
    - stylecheck
    - typecheck
    - unconvert
    - whitespace

run:
  timeout: 1m