package version

import (
	"encoding/json"
	"fmt"
	"runtime"
	"strings"
)

// Info contains versioning information.
type Info struct {
	Version   string `json:"Version"`
	GitCommit string `json:"Commit"`
	BuildDate string `json:"Build"`
	GoVersion string `json:"Go"`
	Platform  string `json:"Platform"`
}

// String returns info as a human-friendly version string.
func (info *Info) String() string {
	if s, err := info.Text(); err == nil {
		return s
	}

	return info.Version
}

// ToJSON returns version information as JSON string.
func (info *Info) ToJSON() string {
	s, _ := json.Marshal(info)

	return string(s)
}

// Text encodes version information into human readable format.
func (info *Info) Text() (string, error) {
	text := strings.Builder{}
	text.WriteString("Version: " + info.Version + "\n")
	text.WriteString("Commit: " + info.GitCommit + "\n")
	text.WriteString("Build: " + info.BuildDate + "\n")
	text.WriteString("Go: " + info.GoVersion + "\n")
	text.WriteString("Platform: " + info.Platform)

	return text.String(), nil
}

// Get returns the overall codebase version information.
func Get() *Info {
	// These variables typically come from -ldflags settings and in their
	// absence fallback to the settings in internal/version/base.go.
	return &Info{
		Version:   Version,
		GitCommit: GitCommit,
		BuildDate: BuildDate,
		GoVersion: runtime.Version(),
		Platform:  fmt.Sprintf("%s/%s", runtime.GOOS, runtime.GOARCH),
	}
}
